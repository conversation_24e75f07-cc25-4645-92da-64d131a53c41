import {Injectable} from '@angular/core';
import {HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse} from '@angular/common/http';
import {Observable, throwError} from 'rxjs';
import {GlobalService} from './shared/data/global.service';
import {catchError, finalize, map} from 'rxjs/operators';
import {ToastrService} from 'ngx-toastr';
import {environment} from '../environments/environment';
import { NgxSpinnerService } from 'ngx-spinner';
import {ActivatedRouteSnapshot, Router} from '@angular/router';
import { CommonConstant } from './shared/constant/common.constant';

@Injectable()
export class AppHttpInterceptor implements HttpInterceptor {

  private excludes = [0, ...environment.excludeStatusCode];

  constructor(private readonly global: GlobalService, private readonly toastService: ToastrService,
              private spinner: NgxSpinnerService, private router: Router, ) {}

  intercept(request: HttpRequest<any>, next: HttpHand<PERSON>): Observable<HttpEvent<any>> {
    let finished = false;

    setTimeout(() => {
      if (!finished) {
        this.spinner.show(undefined,
          {
            type: 'ball-clip-rotate',
            size: 'large',
            bdColor: 'rgba(0, 0, 0, 0)',
            color: '#2B57BB',
            fullScreen: true
          });
      }
    }, 500);

    if (request.url.includes('oauth/token') || request.url.includes('api.ipify.org')) {
      return next.handle(request).pipe(
        finalize(() => {
          finished = true;
          this.spinner.hide();
        })
      );
    }

    console.log('Headers', request.headers);
    console.log('Token', this.global.token);

    if (this.global.token) {
      console.log('Append Token', this.global.token);
      request = request.clone({
        setHeaders: {
          'Authorization': `Bearer ${this.global.token}`,
        }
      });

      console.log('Request Update', request);
    }

    if (request.body && !request.body['audit']) {
      const audit = this.global.audit;
      request = request.clone({
        body: {...request.body, audit}
      });
    }

    if (this.global.user && this.hasTenant(request)) {
      const localTenantCode = localStorage.getItem('tenantCode');
      let tenantCode: any;
      if (request.url.includes("addTenant") || request.url.includes("editTenant")
        || request.url.includes("tenantDetail") || request.url.includes("topup")
        || request.url.includes("getListBalanceTenant") || request.url.includes("updateBalanceTenant")) {
        tenantCode = localTenantCode;
      } else if (request.url.includes("getInquiryAuditTrailSignProcess") || request.url.includes("downloadAuditTrailSignProcess") || request.url.includes("manualReport/s/getList")) {
        tenantCode = null;
      } else if (request.url.includes("getRoleListManagement") || request.url.includes("addRoleManagement")
                || request.url.includes("editRoleManagement")) {
        tenantCode = request.body.tenantCode;
      } else {
        tenantCode = {tenantCode: this.global.user?.role?.tenantCode || localTenantCode};
      }
      request = request.clone({
        body: {...request.body, ...tenantCode}
      });
    }

    console.log('Body Request:', request.body);

    return next.handle(request).pipe(
      map((event: HttpEvent<any>) => {
        if (event instanceof HttpResponse && event.body['status'] &&  this.excludes.indexOf(event.body['status']['code']) === -1) {
          const body = event.body;
            this.toastService.error(body['status']['message'],null, {
                positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
            });
        }
        return event;
      }),
      catchError((error: HttpErrorResponse) => {
        const data = {
          reason: error && error.error && error.error.reason ? error.error.reason : error.message,
          status: error.status
        };

        console.log('http-error', error);
        console.log('url', request.url);

        if (error.status === 401) {
          this.toastService.info('Sesi anda telah habis, silahkan login ulang.', null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          });
          this.router.navigate(['login'], {queryParams: {redirect: this.router.routerState.snapshot.url}});
          console.log('url redirect:', this.router.routerState.snapshot.url);
        } else if (error.status === 0) {
          this.toastService.error('Maaf terjadi timeout, silahkan coba kembali.', null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          });
        } else if (error.status === 429) {
          this.toastService.error('Maaf terlalu banyak request saat ini, silahkan coba kembali.', null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          });
        } else {
          console.log('error', error['error']['error_description']);
          this.toastService.error(error['error']['error_description'] || 'Unknown Error', null, {
            positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
          });
        }

        return throwError(error);
      }),
      finalize(() => {
        finished = true;
        this.spinner.hide();
      })
    )
  }

  private hasTenant(request: HttpRequest<any>) {
    return (request.body && !request.body['tenantCode']) && this.global.user.loginId;
  }
}
